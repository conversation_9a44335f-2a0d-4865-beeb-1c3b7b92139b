import requests
import sys
import os
import src.base.settings as setting
import subprocess

class update:
    def __init__(self):
        pass
    @staticmethod
    def downloadFile():
        print("开始更新，请等待...")
        #检测是否存在pywebview,不存在则sys.executable -m pip install pywebview -i https://mirrors.pku.edu.cn/pypi/web/simple
        try:
            import webview
        except ImportError:
            print("pywebview未安装，正在安装...")
            sysExE=setting.PATH_EXE+"/runtime/python.exe"
            try:
                subprocess.check_call([sysExE, '-m', 'pip', 'install', 'pywebview', "-i https://mirrors.pku.edu.cn/pypi/web/simple"])
            except subprocess.CalledProcessError as e:
                print("pywebview安装失败: ",e)
                return
            print("pywebview部分完成")
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/ftools.zip"
        path=setting.PATH_EXE+"/runtime/ftools.zip"
        r = requests.get(url)
        if r.status_code==200:
            with open(path,'wb') as f:
                f.write(r.content)
            #便利指定目录删除以index开头的文件 第一个assets
            if not os.path.exists(setting.PATH_INTERNAL+"/web/assets"):
                os.makedirs(setting.PATH_INTERNAL+"/web/assets")
            if not os.path.exists(setting.PATH_INTERNAL+"/web/js"):
                os.makedirs(setting.PATH_INTERNAL+"/web/js")
            for file in os.listdir(setting.PATH_INTERNAL+"/web/assets"):
                os.remove(setting.PATH_INTERNAL+"/web/assets/"+file)
            for file in os.listdir(setting.PATH_INTERNAL+"/web/js"):
                os.remove(setting.PATH_INTERNAL+"/web/js/"+file)
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/web.zip" #更新web
            path=setting.PATH_INTERNAL+"/web/web.zip"
            print("更新UI组成部分")
            r = requests.get(url)
            if r.status_code==200:
                with open(path,'wb') as f:
                    f.write(r.content)
            import zipfile
            if os.path.exists(path):
                with zipfile.ZipFile(path, 'r') as z:
                    z.extractall(setting.PATH_INTERNAL+"/web")
                os.remove(path)
        else:
            print("UI组成部分更新失败")
            return
        path_main2=os.path.join(setting.PATH_EXE, 'runtime','main2.pyc')
        if not os.path.exists(path_main2):
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/main2.pyc"
            path=path_main2
            r = requests.get(url)
            if r.status_code==200:
                with open(path,'wb') as f:
                    f.write(r.content)

        path_exe=os.path.join(setting.PATH_EXE, '信小财_new.exe')
        if not os.path.exists(path_exe):
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/信小财_new.exe"
            path=path_exe
            r = requests.get(url)
            if r.status_code==200:
                with open(path,'wb') as f:
                    f.write(r.content)
                

        loadingHtml_path=os.path.join(setting.PATH_INTERNAL, 'web/loading.html')
        if not os.path.exists(loadingHtml_path):
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/loading.html"
            path=loadingHtml_path
            r = requests.get(url)
            if r.status_code==200:
                with open(path,'wb') as f:
                    f.write(r.content)
        if os.path.exists(loadingHtml_path) and os.path.exists(os.path.join(setting.PATH_EXE, '信小财_new.exe')) and os.path.exists(os.path.join(setting.PATH_EXE, '信小财.exe')):
            try:
                import webview
                os.remove(os.path.join(setting.PATH_EXE, '信小财.exe'))
            except Exception as e:
                pass
        print("更新完成，请重启")
