from ast import Await
import src.Gui.callProcess as callProcess
from pydantic import BaseModel
from typing import Dict, Any, List
from datetime import datetime
import src.base.settings as settings
import src.base.cache as cache
from fastapi import HTTPException
from src.web.http import app  
from fastapi import Request
import logging
import traceback
import src.Gui.callProcess as callF
shared_queue = callProcess.shared_queue
@app.get("/api/get-message")
async def get_message():
    try:
        if shared_queue and not shared_queue.empty():
            message_data = shared_queue.get()
            if isinstance(message_data, dict) and "消息" in message_data:
                message_text = message_data["消息"]
                return {"code":200,"message":message_text}
        else:
            return {"code":200,"message":"not need refresh"}
    except Exception as e:
        logging.error(f"Error in get-message: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start-function")
async def start_function(request: Request):
    try:
        data = await request.json()
        if callF.thisProcess.p is not None:
            callF.thisProcess.terminate()
        callF.thisProcess.run(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in start-function: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stop-function")
async def stop_function(request: Request):
    try:
        if callF.thisProcess.p is not None:
            callF.thisProcess.terminate()
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in stop-function: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/get-default-date")
async def get_default_date():
    try:
        return {"code":200,"message":"success","startDate1":cache.Lastdate,"endDate1":cache.Nowdate,"startDate2":cache.theYearFirstDay,"endDate2":cache.Nowdate}
    except Exception as e:
        logging.error(f"Error in get-default-date: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/machine-info")
async def machine_info():
    try:
        data = {"machineCode":callF.regInstance.machine_code,"expiryTime":callF.regInstance.expire_time}
        return data
    except Exception as e:
        logging.error(f"Error in machine-info: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/manual/guide")
async def manual_guide():
    try:
        import webbrowser
        webbrowser.open('https://q2t4357ads.feishu.cn/wiki/H2GHwBZjFiIkF4kKxFTclmTanTg?from=from_copylink')
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in manual-guide: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/register")
async def register(request: Request):
    try:
        data = await request.json()
        newExpireTime=callF.regInstance.register(data["registrationCode"])
        return {"code":200,"message":"success","expiryTime":newExpireTime}
    except Exception as e:
        logging.error(f"Error in register: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/labor-dispatch/template")
async def labor_dispatch_template():
    try:
        from src.utils.fipQuickFill.salary import laborDispatchTemplate
        return {"code":200,"message":"success","data":laborDispatchTemplate()}
    except Exception as e:
        logging.error(f"Error in labor-dispatch-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/labor-dispatch/update")
async def labor_dispatch_update(request: Request):
    try:
        data = await request.json()
        from src.utils.fipQuickFill.salary import laborDispatchUpdate
        laborDispatchUpdate(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in labor-dispatch-update: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/treasury-in-transit/template")
async def treasury_in_transit_template():
    try:
        from src.utils.fundCapital.paymentInTransitQuery import treasuryInTransitTemplate
        return {"code":200,"message":"success","data":treasuryInTransitTemplate()}
    except Exception as e:
        logging.error(f"Error in treasury-in-transit-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/treasury-in-transit/update")
async def treasury_in_transit_update(request: Request):
    try:
        data = await request.json()
        from src.utils.fundCapital.paymentInTransitQuery import treasuryInTransitUpdate
        treasuryInTransitUpdate(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in treasury-in-transit-update: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/book-management/template")
async def book_management_template(request: Request):
    try:
        data = await request.json()
        from src.utils.fipQuickFill.fileCompilation import queryVoucherCount
        return {"code":200,"message":"success","data":queryVoucherCount(data["year"],data["month"],False)}
    except Exception as e:
        logging.error(f"Error in book-management-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
@app.get("/api/book-management/template2")
async def book_management_template2():
    try:
        from src.utils.fipQuickFill.fileCompilation import downloadFile
        return {"code":200,"message":"success","data":downloadFile(False)}
    except Exception as e:
        logging.error(f"Error in book-management-template2: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
@app.post("/api/book-management/save")
async def book_management_save(request: Request):
    try:
        data = await request.json()
        from src.utils.fipQuickFill.fileCompilation import uploadFile
        uploadFile(data["data"]["档案成册管理"])
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in book-management-save: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.get("/api/fund-plan/template1")
async def fund_plan_get_template1():
    try:
        from src.utils.fundCapital.财务一体化资金计划上报 import planUpload
        return {"code":200,"message":"success","data":planUpload().getTemplate(True)}
    except Exception as e:
        logging.error(f"Error in fund-plan-get-template1: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))   

@app.get("/api/fund-plan/template2")
async def fund_plan_get_template2():
    try:
        from src.utils.fundCapital.财务一体化资金计划上报 import planUpload
        return {"code":200,"message":"success","data":planUpload().queryData()}
    except Exception as e:
        logging.error(f"Error in fund-plan-get-template2: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
@app.post("/api/fund-plan/save-template")
async def fund_plan_save_template(request: Request):
    try:
        data = await request.json()
        from src.utils.fundCapital.财务一体化资金计划上报 import planUpload
        planUpload().wirteData(data["templateData"])
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in fund-plan-save-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))  
    
@app.get("/api/budget-report/template")
async def budget_report_template():
    try:
        from src.utils.closeAccountingPeriod.dataprocessing import getCloseDataProcess2
        return {"code":200,"message":"success","data":getCloseDataProcess2()}
    except Exception as e:
        logging.error(f"Error in budget-report-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/budget-report/update")
async def budget_report_update(request: Request):
    try:
        data = await request.json()
        from src.utils.closeAccountingPeriod.dataprocessing import writeDataProcess
        writeDataProcess(data["currentData"])
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in budget-report-update: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
@app.post("/api/budget-report/safety-fee")
async def budget_report_safety_fee(request: Request):
    try:
        from src.utils.closeAccountingPeriod.dataprocessing import safetyFee
        data=safetyFee()
        return {"code":200,"message":"success","data":data}
    except Exception as e:
        logging.error(f"Error in budget-report-safety-fee: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
@app.post("/api/report-assistant/select-last-year-path")
async def select_last_year_path(request: Request):
    try:
        import src.utils.fileui as fileui
        path=fileui.select_file()
        cache.wirteLastYearFinancialReport(path)
        return {"code":200,"message":"success","path":path}
    except Exception as e:
        logging.error(f"Error in select-last-year-path: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/report-assistant/select-current-year-path")
async def select_current_year_path(request: Request):
    try:
        import src.utils.fileui as fileui
        path=fileui.select_file()
        cache.wirteCurrentYearFinancialReport(path)
        return {"code":200,"message":"success","path":path}
    except Exception as e:
        logging.error(f"Error in select-last-year-path: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/import-excel-update-db")
async def import_excel_update_db(request: Request):
    try:
        data = await request.json()
        from src.utils.DB.updateDb import updateToDb
        updateToDb(data["tableName"],True)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in import-excel-update-db: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
        
@app.get("/api/version/local")
async def get_local_version():
    try:
        from src.base.settings import version,status
        return {"code":200,"message":"success","version":version,"status":status}
    except Exception as e:
        logging.error(f"Error in get-local-version: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/get-remote-version")
async def get_remote_version():
    try:
        import requests
        response = requests.get('https://cscec3b-fip.hb11oss.ctyunxs.cn/version.txt')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            items = content.replace('\r\n', '\n').split('\n')
            # 解析为字典
            result = {}
            for item in items:
                # 按等号分割，只分割一次
                key, value = item.split('=', 1)
                result[key] = value
            return {"code":200,"message":"success","versionCode":result["version"],"versionDesc":result["description"]}
        else:
            payloads = {'Context': {'argv':{}}}
            response2=requests.post("https://365.kdocs.cn/api/v3/ide/file/cg4PIdOuXijm/script/V2-1YIfq6wAYTm4hzxl6oQfp8/sync_task",headers={'Content-Type': 'application/json','AirScript-Token':'3pHCBz0yZgm45byLJr4dcW'},json=payloads) 
            if response2.status_code==200:
                return {"code":200,"message":"success","versionCode":response2.json()["data"]['result']["versionCode"],"versionDesc":response2.json()["data"]['result']["versionDesc"]}
            else:
                return {"code":200,"message":"success","versionCode":"未能获取","versionDesc":"未能获取"}
    except Exception as e:
        logging.error(f"Error in get-remote-version: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/get-sap-path")
async def get_sap_path(request: Request):
    try:
        from src.utils.enhance.sapConfig import set_sap_config
        set_sap_config()
        from src.utils.enhance.sapMsg import get_sap_path
        path=get_sap_path()['saplogon_path'].replace("saplogon.exe","sapshcut.exe")
        return {"code":200,"message":"success","path":path}
    except Exception as e:
        logging.error(f"Error in get-sap-path: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/get-sap-account-books")
async def get_sap_account_books(request: Request):
    try:
        from src.utils.enhance.sapName import get_sap_name,wirte_sap_config
        wirte_sap_config()
        return {"code":200,"message":"success","accountBooks":get_sap_name()}
    except Exception as e:
        logging.error(f"Error in get-sap-account-books: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/get-file-path")
async def get_file_path(request: Request):
    try:
        data = await request.json()
        title=data["title"]
        from src.utils.fileui import select_file
        filePath=select_file(title=title)
        return {"code":200,"message":"success","filePath":filePath}
    except Exception as e:
        logging.error(f"Error in get-file-path: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))