Script='''with 
a as (
SELECT any_value(凭证编号) as 凭证编号, 
       any_value(财年) as 财年,
       any_value(利润中心) as 利润中心,
       any_value(利润中心描述) as 利润中心描述,
       any_value(文本) as 文本,
       any_value(过帐日期) as 过帐日期,
       any_value(输入日期) as 输入日期,
       SUM(case when 总账科目长文本 like '专项储备\安全生产费\发生数' then 带符号的本位币金额 else null end) AS 安全生产费,
       any_value(case when 总账科目长文本 like '应付账款%劳务%' or 总账科目长文本 like '应付账款%分包%' then '分包结算安全费' else null end ) AS 类型
FROM 明细帐 
GROUP BY 利润中心,凭证编号,财年)
--
SELECT * FROM a where 安全生产费 > 0.001 or 安全生产费 < -0.001

SELECT any_value(中台单据号) AS 中台单据号,
any_value(自定义的凭证编号) AS 凭证流水号,
ANY_VALUE(过帐日期) AS 过账日期,
any_value(利润中心描述) AS 项目名称
FROM 明细帐 
WHERE 过帐日期 >='2024-07-01 00:00:00' AND 过帐日期<='2024-09-12 00:00:00'
GROUP BY 利润中心,凭证编号



SELECT * FROM 明细帐'''

专项储备='''with 
a as (
SELECT any_value(凭证编号) as 凭证编号, 
       any_value(财年) as 财年,
       any_value(利润中心) as 利润中心,
       any_value(利润中心描述) as 利润中心描述,
       any_value(文本) as 文本,
       any_value(过帐日期) as 过帐日期,
       any_value(输入日期) as 输入日期,
       SUM(case when 总账科目长文本 like '专项储备\安全生产费\发生数' then 带符号的本位币金额 else null end) AS 安全生产费,
       any_value(case when 总账科目长文本 like '应付账款%劳务%' or 总账科目长文本 like '应付账款%分包%' then '分包结算安全费' else null end ) AS 类型
FROM 明细帐 
GROUP BY 利润中心,凭证编号,财年),
b as (
SELECT * FROM a where 安全生产费 > 0.001 or 安全生产费 < -0.001)
INSERT OR REPLACE INTO 专项储备 SELECT * FROM b
'''

主数据写入='''INSERT OR REPLACE
INTO 主数据 SELECT 
array_agg(项目编码),
any_value(利润中心),
any_value(项目所属的核算组织),
any_value(利润中心描述),
any_value(利润中心组描述)
from df group by 利润中心'''

付款台账='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       0-SUM(case when (总账科目长文本 LIKE '%保证金%') then 带符号的本位币金额 ELSE NULL END) AS 扣履约保证金,
       0-SUM(case when (总账科目长文本 LIKE '%供应链融资%' or 总账科目长文本 LIKE '%保理%') then 带符号的本位币金额 ELSE NULL END) AS 供应链保理,
       0-SUM(case when (总账科目长文本 LIKE '%非货币交易%' ) then 带符号的本位币金额 ELSE NULL END) AS 本利润中心,
       0-SUM(case when (总账科目长文本 LIKE '%直接%费%') then 带符号的本位币金额 ELSE NULL END) AS 冲成本,
       0-SUM(CASE WHEN (总账科目长文本 LIKE '%可用存款%' or 总账科目长文本 LIKE '%银行存款%') THEN 带符号的本位币金额 ELSE NULL END) AS 内行或存款,
       any_value(case when 总账科目长文本  like '%可用存款%' then 客户描述 else null end) as 内行客商
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
--
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(总账科目长文本) as 供应商类型,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(输入日期) as 输入日期,
any_value(供应商) as 供应商,any_value(供应商描述) as 供应商描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
sum(带符号的本位币金额) as '总付款金额'
FROM 明细帐
WHERE (总账科目长文本 LIKE '应付账款%款%' and 总账科目长文本 not LIKE '%暂估%') and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,供应商,合同
),
c as (
select  b.财年,过帐日期,输入日期,供应商类型,b.凭证编号,b.利润中心,利润中心描述,供应商,供应商描述,合同,合同文本描述,文本,中台单据号,总付款金额,扣履约保证金,供应链保理,冲成本,本利润中心,内行或存款,内行客商
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年)
INSERT OR REPLACE INTO 付款台账 SELECT * FROM c'''

内行查询='''WITH a AS (
    SELECT  ANY_VALUE(凭证编号) as 凭证编号, ANY_VALUE(利润中心) as 利润中心, ANY_VALUE(财年) as 财年,any_value(客户描述) as 客户描述,
    any_value(客户) as 客户,
    count(distinct (CASE WHEN 总账科目长文本 like '%可用存款%' THEN 客户 ELSE NULL END)) as 内行客商数量,
    any_value(CASE WHEN 总账科目长文本 like '银行存款%' THEN 1 ELSE 0 END) as 是否含银行存款,
    any_value(CASE WHEN 总账科目长文本 like '可用存款%' THEN 1 ELSE 0 END) as 是否含内行存款,
    FROM 明细帐
    WHERE (总账科目长文本 LIKE '%存款%') 
    GROUP BY 利润中心, 凭证编号, 财年
),
b AS (
select 明细帐.*,
(case when a.内行客商数量 > 1  then 明细帐.客户 else a.客户 end) as 内行客商,
(case when a.内行客商数量 > 1  then 明细帐.客户描述 else a.客户描述 end) as 内行客商描述,
a.内行客商数量
from 明细帐 LEFT join a on a.利润中心 = 明细帐.利润中心 and a.凭证编号 = 明细帐.凭证编号 and a.财年 = 明细帐.财年
where ((a.内行客商数量 > 1 or (是否含银行存款>0 and 是否含内行存款>0) and 总账科目长文本  like '%存款%') or (a.内行客商数量 == 1 and 总账科目长文本 not like '%存款%' and 是否含银行存款==0)
or (是否含银行存款==1 and 是否含内行存款==0 and 总账科目长文本 not like '%存款%')
)
),
c as (
select 
any_value(财年) as 财年,
any_value(过帐日期) as 过帐日期,
any_value(输入日期) as 输入日期,
any_value(凭证编号) as 凭证编号, 
any_value(利润中心) as 利润中心, 
any_value(利润中心描述) as 利润中心描述,
any_value(总账科目长文本) as 总账科目长文本,
sum(case when 内行客商数量 > 1  then 带符号的本位币金额 else 0-带符号的本位币金额 end) as 内行金额,
any_value(内行客商) as 内行客商,
any_value(内行客商描述) as 内行客商描述,
any_value(内行客商数量) as 内行客商数量,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(中台单据号) as 中台单据号,
any_value(文本) as 事由
from b
GROUP by 凭证编号, 利润中心, 财年,总账科目长文本,内行客商)
--
INSERT OR REPLACE INTO 内行查询 SELECT * FROM c
'''

内行查询资金整理='''with a as(
SELECT  
any_value(内行查询.财年) as 财年,
any_value(内行查询.过帐日期) as 过帐日期,
any_value(内行查询.利润中心) as 利润中心,
any_value(内行查询.利润中心描述) as 利润中心描述,
any_value(内行查询.凭证编号) as 凭证编号,
any_value(内行查询.中台单据号) as 中台单据号2,
array_agg(DISTINCT(内行查询.总账科目长文本)) as 总账科目,
array_agg(DISTINCT(内行查询.供应商描述)) as 供应商描述,
any_value(事由) as 事由,
sum(内行查询.内行金额) as 内行金额,
any_value(CASE WHEN 项目所属的核算组织 is NULL THEN  '外部'  ELSE  '内部' END) as 内外部流水,
if_classification(总账科目,中台单据号2) as 单据分类,
any_value(内行客商) as 内行客商标记,
any_value(内行客商描述) as 内行客商标记2
from 内行查询 left join 主数据  on 内行查询.内行客商=主数据.项目所属的核算组织
GROUP BY 凭证编号,财年,内行查询.利润中心,内行客商)
--
INSERT OR REPLACE INTO 资金整理 SELECT * FROM a'''

内部对账='''with a as (
select any_value(客商编码) as 客商编码,
any_value(客商名称) as 客商名称,
any_value(过帐日期) as 过帐日期,
any_value(凭证编号) as 凭证编号,
any_value(行项目文本) as 事由,
0-sum(case when 科目描述 like '应付账款\分包工程款\%' and 借贷标识=='贷方'  and 科目描述 not like '%暂估%' then 本币金额 else 0 end) as 结算额,
sum(case when 科目描述 like '应付账款\分包工程款\%' and 借贷标识=='借方' and 科目描述 not like '%暂估%' then 本币金额 else 0 end) as 付款额,
0-sum(case when 科目描述 like '应付账款\分包工程款\%暂估%' and 借贷标识=='贷方'  then 本币金额 else 0 end) as 暂估额
from df
where 凭证编号 not like '10%' and 凭证编号 not like '20%' and 凭证编号 not like '99%'
group by 客商编码,过帐日期,凭证编号)
--
select a.*,主数据.利润中心 from a left join 主数据 on a.客商编码=主数据.项目所属的核算组织
'''

内部对账汇总='''select any_value(客商编码) as 客商编码,
any_value(客商名称) as 客商名称,
any_value(过帐日期) as 过帐日期,
any_value(凭证编号) as 凭证编号,
any_value(行项目文本) as 事由,
0-sum(case when 科目描述 like '应付账款\分包工程款\%' and 借贷标识=='贷方'  and 科目描述 not like '%暂估%' then 本币金额 else 0 end) as 结算额,
sum(case when 科目描述 like '应付账款\分包工程款\%' and 借贷标识=='借方' and 科目描述 not like '%暂估%' then 本币金额 else 0 end) as 付款额,
sum(case when 科目描述 like '应付账款\分包工程款\%暂估%' and 借贷标识=='贷方'  then 本币金额 else 0 end) as 暂估额
from df
where 凭证编号 not like '10%' and 凭证编号 not like '20%' and 凭证编号 not like '99%'
group by 客商编码,过帐日期,凭证编号
'''

凭证查询='''with a as (
select DISTINCT(凭证编号) from 明细帐 where 总账科目长文本 like '主营业务收入%' and 公司代码='3169'
)
select a.*,明细帐.合同文本描述 from a left join 明细帐 on a.凭证编号=明细帐.凭证编号

'''

分供结算台账='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       SUM(case when (总账科目长文本 LIKE '%进项税%') then 带符号的本位币金额 ELSE NULL END) AS 进项税
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
--
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(总账科目长文本) as 供应商类型,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(输入日期) as 输入日期,
any_value(供应商) as 供应商,any_value(供应商描述) as 供应商描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
0-sum(带符号的本位币金额) as 含税结算金额
FROM 明细帐
WHERE (总账科目长文本 LIKE '应付账款%款%' and 总账科目长文本 not LIKE '%暂估%') and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,供应商,合同
),
c as (
select  b.财年,过帐日期,输入日期,供应商类型,b.凭证编号,b.利润中心,利润中心描述,供应商,供应商描述,合同,合同文本描述,文本,中台单据号,含税结算金额,进项税
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年)
INSERT OR REPLACE INTO 分供结算台账 SELECT * FROM c'''

分包分供台账='''select 
any_value(项目名称||合同编号) as 查找主键,
any_value(组织机构名称) as 组织机构名称,
any_value(项目名称) as 项目名称,
any_value(项目编号) as 项目编号,
any_value(合同名称) as 合同名称,
any_value(合同编号) as 合同编号,
any_value(原合同编号) as 原合同编号,
any_value(合同业务内容) as 合同业务内容,
any_value(客商名称) as 客商名称,
any_value(客商编号) as 客商编号,
any_value(合同类型) as 合同类型,
sum("合同金额(含税)") as 合同金额,
any_value(税率) as 税率,
sum("累计已结算金额(含税)") as 结算金额,
sum("预收/预付余额") as 预付金额,
sum("累计已收/付金额(含税)") as 已付金额,
sum("累计发票金额(含税)") as 发票金额,
max("约定付款比例")as 付款比例,
(结算金额*付款比例) as 按比例应付余额,
(结算金额*付款比例-已付金额) as 拖欠款
from df
GROUP by  项目名称,合同编号

'''

外部确权台账='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       0-SUM(case when (总账科目长文本 LIKE '%销项%') then 带符号的本位币金额 ELSE NULL END) AS 销项税
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
--
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(总账科目长文本) as 供应商类型,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(输入日期) as 输入日期,
any_value(客户) as 客户,any_value(客户描述) as 客户描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
sum(带符号的本位币金额) as 含税结算金额
FROM 明细帐
WHERE (总账科目长文本 LIKE '应收账款%' or 总账科目长文本 LIKE '合同资产%质保金%') and 总账科目长文本 not like '%税%' and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,客户,合同
),
c as (
select  b.财年,过帐日期,输入日期,b.凭证编号,b.利润中心,利润中心描述,客户,客户描述,合同,合同文本描述,文本,中台单据号,含税结算金额,销项税
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年)
INSERT OR REPLACE INTO 外部确权台账 SELECT * FROM c'''

常用sql语句='''
select 
利润中心描述,总账科目长文本,
sum(case when (过帐日期<"2019-01-01 00:00:00")  then 带符号的本位币金额 else null end) as "期初余额",
sum(带符号的本位币金额) as "期末余额",
sum(case when ((("记账方向" = "H" and 反记账 = "") or ("记账方向" = "S" and 反记账 = "X")) and ((过帐日期 >= "2019-01-01 00:00:00") and (过帐日期<="2024-04-30 00:00:00")) ) then 带符号的本位币金额 else null end) as "借方发生额",
sum(case when ((("记账方向" = "S" and 反记账 = "") or ("记账方向" = "H" and 反记账 = "X")) and ((过帐日期 >= "2019-01-01 00:00:00") and (过帐日期<="2024-04-30 00:00:00"))) then 带符号的本位币金额 else null end) as "贷方发生额"
from 明细帐 where 文本 != "自动清账剩余项目" and 利润中心描述 like "%金控%" GROUP BY 总账科目长文本,利润中心
#科目余额表


WITH a AS (
    SELECT  凭证编号, 利润中心, 财年
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "应付账款%款%") and (文本!="自动清账剩余项目")
    AND (("记账方向" = "S" AND 反记账 = "") OR ("记账方向" = "H" AND 反记账 = "X"))  GROUP BY 利润中心, 凭证编号, 财年
),
--
b as (
SELECT a.凭证编号, a.财年,a.利润中心,
       SUM(case when (总账科目长文本 LIKE "%直接%费%") then 明细帐.带符号的本位币金额 ELSE NULL END) AS "冲成本",
       SUM(CASE WHEN (明细帐.总账科目长文本 LIKE "%可用存款%") THEN 明细帐.带符号的本位币金额 ELSE NULL END) AS "内行"
FROM a
LEFT JOIN  明细帐 ON a.利润中心 = 明细帐.利润中心 AND a.凭证编号 = 明细帐.凭证编号 AND a.财年 = 明细帐.财年
GROUP BY a.利润中心, a.凭证编号, a.财年 ),
--
c as (
SELECT  凭证编号, 利润中心, 财年, 客户, 客户描述
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "%可用存款") 
 GROUP BY 利润中心, 凭证编号, 财年),
 --
d as (
select * from b LEFT  JOIN c ON b.利润中心 = c.利润中心 AND b.凭证编号 = c.凭证编号 AND b.财年 = c.财年),
--
e as (SELECT  凭证编号, 利润中心, 财年,供应商,供应商描述,文本,利润中心描述,过帐日期,中台单据号,
sum(总账科目长文本) as "总付款金额"
FROM 明细帐
WHERE (总账科目长文本 LIKE "应付账款%款%") and (文本!="自动清账剩余项目")
AND (("记账方向" = "S" AND 反记账 = "") OR ("记账方向" = "H" AND 反记账 = "X"))  GROUP BY 利润中心, 凭证编号, 财年,供应商)
--
SELECT * from d LEFT  JOIN e ON d.利润中心 = e.利润中心 AND d.凭证编号 = e.凭证编号 AND d.财年 = e.财年

sqllite默认是内连接，务必记得使用left join



WITH a AS (
    SELECT  凭证编号, 利润中心, 财年
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "%供应链%") 
    GROUP BY 利润中心, 凭证编号, 财年
)
--
SELECT a.凭证编号, a.利润中心, a.财年,文本,利润中心描述,过帐日期,中台单据号,总账科目长文本,带符号的本位币金额,供应商,供应商描述,客户,客户描述 from a LEFT  JOIN 明细帐 ON a.利润中心 = 明细帐.利润中心 AND a.凭证编号 = 明细帐.凭证编号 AND a.财年 = 明细帐.财年



WITH a AS (
    SELECT  凭证编号, 利润中心, 财年
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "%内部往来%其他%") 
    GROUP BY 利润中心, 凭证编号, 财年
),
--
 b AS (
    SELECT  凭证编号, 利润中心, 财年
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "%存款%") 
    GROUP BY 利润中心, 凭证编号, 财年
),
c AS (
    SELECT  *
    from a   JOIN b ON a.利润中心 = b.利润中心 AND a.凭证编号 = b.凭证编号 AND a.财年 = b.财年
)
--
SELECT c.凭证编号, c.利润中心, c.财年,文本,利润中心描述,过帐日期,中台单据号,总账科目长文本,带符号的本位币金额,供应商,供应商描述,客户,客户描述 from c LEFT  JOIN 明细帐 ON c.利润中心 = 明细帐.利润中心 AND c.凭证编号 = 明细帐.凭证编号 AND c.财年 = 明细帐.财年'''

应付汇总按供应商='''WITH a AS (
select 
any_value(利润中心描述) as 利润中心名称,
any_value(利润中心) as 利润中心,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(CASE WHEN 总账科目长文本 LIKE '应付账款%' AND 总账科目长文本 NOT LIKE '%进项税%'  THEN 总账科目长文本 ELSE NULL END ) AS 业务类型,
0-sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目')then 带符号的本位币金额 else null end) as 累计结算,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目' )then 带符号的本位币金额 else null end) as 累计付款,
0-sum(case when (总账科目长文本 like '其他应付款%保证金%' ) then 带符号的本位币金额 else null end) as 保证金余额,
sum(case when (总账科目长文本 like '其他应收款%进项税%' or 总账科目长文本 like '应付账款%进项税%' ) then 带符号的本位币金额 else null end) as 进项税余额,
FROM 明细帐 WHERE (总账科目长文本 LIKE '%进项税%' OR 总账科目长文本 LIKE '应付账款%' OR 总账科目长文本 LIKE '其他应付款%') AND 总账科目长文本 NOT LIKE '%供应链融资%' AND 总账科目长文本 NOT LIKE '%暂估%'
GROUP by  利润中心,供应商),
b AS (
    select any_value(项目名称) as 项目名称,
    any_value(客商编号) as 客商编号,
    any_value(税率) as 税率,
    sum(结算金额) as 结算金额,
    sum(已付金额) as 已付金额,
    sum(发票金额) as 发票金额
    from 一体化合同台账
    GROUP BY 项目名称,客商名称
)
--
SELECT a.*,税率,结算金额,已付金额,发票金额 FROM  a left join b on 利润中心名称=项目名称 and 供应商=客商编号 WHERE 累计结算>0.001 OR 累计结算<-0.001'''

应付汇总按合同='''select 
any_value(利润中心描述) as 项目名称,
any_value(利润中心) as 利润中心,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(总账科目长文本) AS 业务类型,
any_value(合同) as 合同编号,
any_value(合同文本描述) as 合同文本描述,
0-sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目')then 带符号的本位币金额 else null end) as 累计结算,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目' )then 带符号的本位币金额 else null end) as 累计付款
FROM 明细帐 WHERE 总账科目长文本 LIKE '应付账款%' AND 总账科目长文本 NOT LIKE '%供应链%' AND 总账科目长文本 NOT LIKE '%进项%'
GROUP by  利润中心,供应商,合同'''

开票计算='''select sum(开票额) from (
select  
CAST(regexp_extract(税码描述, '[0-9]+') AS INT) AS extracted_numbers,
-带符号的本位币金额 as 销项税额,
round(销项税额/extracted_numbers*100,2) as 开票额,
文本
from 明细帐 
where 过帐日期 >= '2025-01-01'
and 总账科目长文本 like '%应交税费%销%' and (税码描述 like '销项%3%' or 税码描述 like '销项%9%' or 税码描述 like '销项%6%' or 税码描述 like '销项%7%' or 税码描述 like '销项%1%' ))'''

快速科目余额表='''select 
any_value(明细帐.总账科目) as 总账科目,
any_value(明细帐.总账科目长文本) as 总账科目长文本,
sum(case when (过帐日期<'期初日期留')  then 带符号的本位币金额 else null end) as 期初余额,
sum(case when (过帐日期<='期末日期留')  then 带符号的本位币金额 else null end) as 期末余额,
sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留') and (过帐日期<='期末日期留')) ) then 带符号的本位币金额 else null end) as 本期贷方发生额,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留') and (过帐日期<='期末日期留'))) then 带符号的本位币金额 else null end) as 本期借方发生额,
any_value(科目分类2) as 科目再分类,
any_value(利润中心组描述) as 利润中心组描述
from 明细帐 left join 主数据 on 明细帐.利润中心 = 主数据.利润中心 left join 科目对照 on 明细帐.总账科目长文本=科目对照.总账科目长文本  where 文本 != '自动清账剩余项目'  GROUP BY 明细帐.总账科目长文本,利润中心组描述'''

快速科目余额表余额表='''select 
any_value(科目余额表第二期.总账科目) as 总账科目,
any_value(科目余额表第二期.总账科目长文本) as 总账科目长文本,
round(sum(期初金额),4) as 期初余额,
round(sum(期末余额),4) as 期末余额,
round(sum(本期贷方金额),4) as 本期贷方发生额,
round(sum(本期借方金额),4) as 本期借方发生额,
any_value(科目分类2) as 科目再分类,
any_value(利润中心组名称) as 利润中心组描述
from 科目余额表第二期 left join 科目对照 on 科目余额表第二期.总账科目长文本=科目对照.总账科目长文本   GROUP BY 科目余额表第二期.总账科目长文本,利润中心组名称'''

成本查询='''with 
a as (
SELECT any_value(凭证编号) as 凭证编号, 
       any_value(中台单据号) as 中台单据号, 
       any_value(财年) as 财年,
       any_value(利润中心) as 利润中心,
       any_value(利润中心描述) as 利润中心描述,
       any_value(文本) as 文本,
       any_value(过帐日期) as 过帐日期,
       any_value(输入日期) as 输入日期,
       any_value(总账科目长文本) as 成本科目,
       SUM(带符号的本位币金额) AS 入账成本
FROM 明细帐 where (总账科目长文本 LIKE '%合同履约成本%' 
or 总账科目长文本 LIKE '管理费用%'
or 总账科目长文本 LIKE '信用减值损失%'
or 总账科目长文本 LIKE '资产减值损失%'
or 总账科目长文本 LIKE '研发费用%'
or 总账科目长文本 LIKE '税金及附加%'
or 总账科目长文本 LIKE '资产处置损益%'
or 总账科目长文本 LIKE '财务费用%'
or 总账科目长文本 LIKE '营业外支出%') 
and 总账科目长文本 not like '%结转%'
GROUP BY 利润中心,凭证编号,财年,总账科目长文本),
b as (SELECT any_value(凭证编号) as 凭证编号, 
any_value(利润中心) as 利润中心, 
any_value(财年) as 财年, 
any_value(case when (供应商 != '') then 供应商 ELSE NULL END) as 供应商,
any_value(case when (供应商描述 != '') then 供应商描述 ELSE NULL END) as 供应商描述,
any_value(case when (总账科目长文本 LIKE '%暂估%') then '暂估成本' when (总账科目长文本 LIKE '%内部往来%' ) then '内部往来划转' ELSE NULL END) AS 科目分类
FROM 明细帐
GROUP BY 利润中心,凭证编号,财年
),
c as (
select  a.财年,a.过帐日期,a.输入日期,a.凭证编号,a.中台单据号,a.利润中心,a.利润中心描述, a.成本科目,b.供应商,b.供应商描述,a.文本,a.入账成本,b.科目分类
from a LEFT JOIN b ON a.利润中心 = b.利润中心 AND a.凭证编号 = b.凭证编号 AND a.财年 = b.财年 where a.入账成本 > 0.001 or a.入账成本 < -0.001)
--
INSERT OR REPLACE INTO 成本表 SELECT * EXCLUDE (总账科目长文本 , 科目方向 , 科目分类2) FROM c left join 科目对照 on c.成本科目 = 科目对照.总账科目长文本'''

抵销检查='''
with a as (
select 
(case when 总账科目长文本 like '应收%' or 总账科目长文本 like '应付%' then '应收应付' else (case when (总账科目长文本 like '其他应付%' or 总账科目长文本 like '其他应收%') and 总账科目长文本 not like '%内部往来%' and 总账科目长文本 not like '%可用存款%' then '其他应收应付' else 总账科目长文本 end) end) as 总账科目长文本,
df.利润中心 as 利润中心,利润中心名称,df.客户,客户名称,期末余额,dfBasedata.利润中心 as 利润中心2 
from df Left join dfBasedata on df.客户 = dfBasedata.项目所属的核算组织
where (总账科目长文本 like '%内部往来%' or 总账科目长文本 like '%应收%' or 总账科目长文本 like '%应付%' or 总账科目长文本 like '%可用存款%') and 利润中心2 != ''),
--
b as (
select any_value(利润中心名称) as 利润中心名称,any_value(总账科目长文本) as 总账科目长文本 ,any_value(利润中心) as 利润中心,any_value(利润中心2) as 利润中心2,any_value(客户) as 客户,any_value(客户名称) as 客户名称,sum(期末余额) as 期末余额 from a
GROUP by 总账科目长文本,利润中心,利润中心2,客户
),
--
c as (
    select * from b
),
--
d as (
select b.总账科目长文本,b.利润中心,b.利润中心名称,b.客户,b.客户名称,b.期末余额,b.利润中心2,c.利润中心名称 as 对方,c.期末余额 as 对方期末余额,(b.期末余额 + c.期末余额) as 差额 from b left join c on b.利润中心 = c.利润中心2 and b.总账科目长文本 =  c.总账科目长文本 and b.利润中心2 = c.利润中心
)
--
select d.*,dfBasedata.利润中心组描述 from d left join dfBasedata on d.利润中心 = dfBasedata.利润中心 where 差额 > 0.001 or  差额 < -0.001 or (对方期末余额 is null and (期末余额>0.001 or 期末余额<-0.001))'''

指标计算='''SELECT -sum(case when (明细帐.总账科目长文本 like '主营业务收入%' or 明细帐.总账科目长文本 like '其他业务收入%') and 过帐日期 >= '2025-01-01' then 明细帐.带符号的本位币金额 else 0 end) as 本年收入,
-sum(case when (科目分类2 like '%本年利润合计%') and 过帐日期 >= '2025-01-01' then 明细帐.带符号的本位币金额 else 0 end) as 本年利润,
sum(case when (明细帐.总账科目长文本 like '%存款%') then 明细帐.带符号的本位币金额 else 0 end) as 资金,
sum(case when (科目分类1 like '资产') then 明细帐.带符号的本位币金额 else 0 end) as 总资产,
sum(case when (科目分类1 like '负债') then 明细帐.带符号的本位币金额 else 0 end) as 总负债

from 明细帐 left join 科目对照 on 明细帐.总账科目长文本=科目对照.总账科目长文本


'''

按利润中心计算主要科目金额='''-- Active: 1745400258741@@127.0.0.1@3306
with 
a as (
select 
any_value(利润中心组名称) as 利润中心组名称,
any_value(利润中心名称) as 利润中心名称,
any_value(WBS元素描述) as 项目名称,
any_value(WBS元素) as WBS元素,
any_value(利润中心) as 利润中心,
round(sum(case when 科目余额表第二期.总账科目长文本 like '主营业务收入%'  then 0-期末余额 else 0 end),4) as 收入,
round(sum(case when 科目余额表第二期.总账科目长文本 like '主营业务成本%'  then 期末余额 else 0 end),4) as 成本,
round(sum(case when 科目余额表第二期.总账科目长文本 like '主营业务收入%'  then 0-期初金额 else 0 end),4) as 年初收入,
round(sum(case when 科目余额表第二期.总账科目长文本 like '主营业务成本%'  then 期初金额 else 0 end),4) as 年初成本,
round(sum(case when 科目余额表第二期.总账科目长文本 like '专项储备%'  then 期末余额 else 0 end),4) as 专项储备余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%合同结算%' or 科目余额表第二期.总账科目长文本 like '合同资产\工程款（已完工未结算）' or 科目余额表第二期.总账科目长文本 like '%已结算未完工%' then 期末余额 else 0 end),4) as 合同余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '合同履约成本%' and 科目余额表第二期.总账科目长文本 not like '合同履约成本%结转%' then 期末余额 else 0 end),4) as 合同履约成本余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%预计负债\亏损合同%'  then 期末余额 else 0 end),4) as 预计负债亏损合同,
round(sum(case when 科目余额表第二期.总账科目长文本 like '原材料%'  then 期末余额 else 0 end),4) as 原材料,
round(sum(case when 科目余额表第二期.总账科目长文本 like '合同履约成本%'  then 期末余额 else 0 end),4) as 成本余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款%暂估%'  then 期末余额 else 0 end),4) as 暂估应付余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '研发支出%'  then 期末余额 else 0 end),4) as 研发支出,
round(sum(case when 科目余额表第二期.总账科目长文本 like '内部存款\非货币交易'  then 期末余额 else 0 end),4) as 本利润非货币交易,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%可用存款%' or 科目余额表第二期.总账科目长文本 like '银行存款%'  then 期末余额 else 0 end),4) as 原始存量,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\内部借贷%'  then 期末余额 else 0 end),4) as 内部借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款\应付供应链融资款%'  then 期末余额 else 0 end),4) as 保理借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\其他' and 客户名称 in ('总部客商名称') then 期末余额 else 0 end),4) as 内部往来挂总部,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\其他' and 客户名称 in ('总部客商名称') then 期初金额 else 0 end),4) as 期初内部往来挂总部,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%现场维护费'then 期末余额 else 0 end),4) as 现场维护费,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\其他' then 期末余额 else 0 end),4) - 现场维护费 as 内部往来挂经理部,
内部往来挂经理部+现场维护费 as 内部往来需调整,
round(sum(case when (科目余额表第二期.总账科目长文本 like '应收账款%' or 科目余额表第二期.总账科目长文本 like '合同资产%质保金') and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年确权额,
round(sum(case when (科目余额表第二期.总账科目长文本 like '应收账款%' or 科目余额表第二期.总账科目长文本 like '合同资产%质保金') and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年收款额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款%' and 科目余额表第二期.总账科目长文本 like '应付账款%供应链%'  and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年分供结算,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款%' and 科目余额表第二期.总账科目长文本 like '应付账款%供应链%'  and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年分供付款,
sum(case when 科目余额表第二期.总账科目长文本 like '%分包工程支出%' then 期末余额 else 0 end) as 累计分包工程支出,
sum(case when 科目余额表第二期.总账科目长文本 like '%直接人工费%' then 期末余额 else 0 end) as 累计直接人工费,
sum(case when 科目余额表第二期.总账科目长文本 like '%直接材料费%' then 期末余额 else 0 end) as 累计直接材料费,
sum(case when 科目余额表第二期.总账科目长文本 like '%机械使用费%' then 期末余额 else 0 end) as 累计机械使用费,
sum(case when 科目余额表第二期.总账科目长文本 like '%其他直接费用%' then 期末余额 else 0 end) as 累计其他直接费用,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%可用存款%' or 科目余额表第二期.总账科目长文本 like '银行存款%'  then 期初金额 else 0 end),4) as 期初原始存量,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\内部借贷%'  then 期初金额 else 0 end),4) as 期初内部借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款\应付供应链融资款%'  then 期初金额 else 0 end),4) as 期初保理借款,
0-sum(case when 科目分类2 like '%利润%' then 期末余额 else 0 end) as 未含本期毛利累计利润,
0-sum(case when 科目分类2 like '%利润%'  then 期初金额 else 0 end) as 年初累计利润
from 科目余额表第二期
LEFT JOIN 科目对照 on 科目余额表第二期.总账科目长文本=科目对照.总账科目长文本
WHERE WBS元素 != '' and WBS元素 not like 'QCQH%'
GROUP by  利润中心,WBS元素),
b as (
select
any_value(WBS元素) as WBS元素,
any_value(利润中心) as 利润中心,
round(sum(case when (总账科目长文本 like '应收账款%' or 总账科目长文本 like '合同资产%质保金') and 总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年确权额,
round(sum(case when (总账科目长文本 like '应收账款%' or 总账科目长文本 like '合同资产%质保金') and 总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年收款额,
round(sum(case when 总账科目长文本 like '应付账款%' and 总账科目长文本 like '应付账款%供应链%'  and 总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年分供结算,
round(sum(case when 总账科目长文本 like '应付账款%' and 总账科目长文本 like '应付账款%供应链%'  and 总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年分供付款
from 科目余额表第一期
WHERE WBS元素 != '' and WBS元素 not like 'QCQH%'
GROUP by  利润中心,WBS元素
),
c as (
select 
concat(a.利润中心,a.WBS元素) as 定位符,
利润中心组名称,利润中心名称,项目名称,a.WBS元素,a.利润中心,收入,成本,年初收入,年初成本,专项储备余额,合同余额,合同履约成本余额,预计负债亏损合同,原材料,成本余额,暂估应付余额,研发支出,本利润非货币交易,原始存量,内部借款,保理借款,
内部往来挂总部,
内部往来挂经理部,
现场维护费,
内部往来需调整,
a.本年确权额+b.本年确权额 as 累计确权,
a.本年收款额+b.本年收款额 as 累计收款,
a.本年分供结算+b.本年分供结算 as 累计分供结算,
a.本年分供付款+b.本年分供付款 as 累计分供付款,
累计分包工程支出,
累计直接人工费,
累计直接材料费,
累计机械使用费,
累计其他直接费用,
期初原始存量,
期初内部借款,期初保理借款,期初内部往来挂总部,
期初原始存量+期初内部借款+期初保理借款+期初内部往来挂总部 as 期初快速资金存量,
原始存量+内部借款+保理借款+内部往来挂总部 as 快速资金存量,
累计收款 as 开累资金流入,
累计收款-快速资金存量 as 开累资金流出,
a.本年收款额 as 本期收款,
期初快速资金存量+本期收款-快速资金存量 as 本期流出,
未含本期毛利累计利润,
年初累计利润
from a left JOIN b on a.WBS元素 = b.WBS元素 and a.利润中心 = b.利润中心
)
select * from c'''

按利润中心计算余额明细帐='''with a as(
select 
any_value(利润中心描述) as 利润中心描述,
any_value(利润中心) as 利润中心,
sum(case when 总账科目长文本 like '主营业务收入%'  then 0-带符号的本位币金额 else 0 end) as 收入,
sum(case when 总账科目长文本 like '主营业务成本%'  then 带符号的本位币金额 else 0 end) as 成本,
sum(case when 总账科目长文本 like '专项储备%'  then 带符号的本位币金额 else 0 end) as 专项储备余额,
sum(case when 总账科目长文本 like '%合同结算%' or 总账科目长文本 like '合同资产\工程款（已完工未结算）' or 总账科目长文本 like '%已结算未完工%' then 带符号的本位币金额 else 0 end) as 合同余额,
sum(case when 总账科目长文本 like '合同履约成本%' and 总账科目长文本 not like '合同履约成本%结转%' then 带符号的本位币金额 else 0 end) as 合同履约成本余额,
sum(case when 总账科目长文本 like '%预计负债\亏损合同%'  then 带符号的本位币金额 else 0 end) as 预计负债亏损合同,
sum(case when 总账科目长文本 like '原材料%'  then 带符号的本位币金额 else 0 end) as 原材料,
sum(case when 总账科目长文本 like '合同履约成本%'  then 带符号的本位币金额 else 0 end) as 成本余额,
sum(case when 总账科目长文本 like '应付账款%暂估%'  then 带符号的本位币金额 else 0 end) as 暂估应付余额,
sum(case when 总账科目长文本 like '研发支出%'  then 带符号的本位币金额 else 0 end) as 研发支出,
sum(case when 总账科目长文本 like '内部存款\非货币交易'  then 带符号的本位币金额 else 0 end) as 本利润非货币交易未平,
sum(case when 总账科目长文本 like '%可用存款%' or 总账科目长文本 like '银行存款%'  then 带符号的本位币金额 else 0 end) as 原始存量,
sum(case when 总账科目长文本 like '%内部往来\内部借贷%'  then 带符号的本位币金额 else 0 end) as 内部借款,
sum(case when 总账科目长文本 like '应付账款\应付供应链融资款'  then 带符号的本位币金额 else 0 end) as 保理借款,
sum(case when (总账科目长文本 like '%内部往来\其他' or 总账科目长文本 like '%内部往来\代垫费用')  and (客户描述 in ('总部客商名称')) then 带符号的本位币金额 else 0 end) as 内部往来挂总部,
sum(case when (总账科目长文本 like '%内部往来\其他' or 总账科目长文本 like '%内部往来\代垫费用') then 带符号的本位币金额 else 0 end) - 内部往来挂总部 as 内部往来挂经理部,
sum(case when 总账科目长文本 like '机关划转费用科目'then 带符号的本位币金额 else 0 end) as 现场维护费,
内部往来挂经理部+现场维护费 as 内部往来需调整,
sum(case when 总账科目长文本 like '%待转销%'then 带符号的本位币金额 else 0 end) as 销项税余额,
sum(case when ((总账科目长文本 like '应收账款%进度%' or 总账科目长文本 like '合同资产%质保金%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计确权,
0-sum(case when ((总账科目长文本 like '应收账款%进度%' or 总账科目长文本 like '合同资产%质保金%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计收款,
0-sum(case when ((总账科目长文本 like '应付账款%' and 总账科目长文本 not like '%税%' and 总账科目长文本 not like '%暂估%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计分供结算,
sum(case when ((总账科目长文本 like '应付账款%' and 总账科目长文本 not like '%税%' and 总账科目长文本 not like '%暂估%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计分供付款
FROM 明细帐
GROUP by  利润中心),
b as (
select sum(case when 成本类别 == '商务间接费用'  then 入账成本 else 0 end) as 商务间接费用,
sum(case when 成本类别 == '商务机械费用'  then 入账成本 else 0 end) as 商务机械费用,
sum(case when 成本类别 == '商务直接税金' then 入账成本 else 0 end) as 附加税,
any_value(利润中心) AS 利润中心
from 成本表 GROUP by 利润中心),
c as (
select sum(case when 类型 IS NULL  then 安全生产费 else 0 end) as 非分包安全费,
any_value(利润中心) AS 利润中心
from 专项储备 GROUP by 利润中心),
d as (select sum(结算额) as 总包结算额,
sum(付款额) as 总包付款额,
sum(暂估额) as 总包暂估额,
any_value(利润中心) AS 利润中心
from 内部对账
GROUP by 利润中心)
--
select a.*,b.商务间接费用,b.商务机械费用,b.附加税,c.非分包安全费,d.总包结算额,d.总包付款额,d.总包暂估额 from a left join b on a.利润中心 = b.利润中心 left join c on a.利润中心 = c.利润中心 left join d on a.利润中心 = d.利润中心
'''

按利润中心计算余额明细帐结账用='''select 
concat(明细帐.利润中心,明细帐.WBS元素) as 定位符,
any_value(主数据.利润中心组描述) as 利润中心组名称,
any_value(明细帐.利润中心描述) as 利润中心名称,
any_value(明细帐.WBS元素描述) as 项目名称,
any_value(明细帐.WBS元素) as 项目编码,
any_value(明细帐.利润中心) as 利润中心,
sum(case when 明细帐.总账科目长文本 like '主营业务收入%'  then 0-带符号的本位币金额 else 0 end) as 收入,
sum(case when 明细帐.总账科目长文本 like '主营业务成本%'  then 带符号的本位币金额 else 0 end) as 成本,
sum(case when 明细帐.总账科目长文本 like '主营业务收入%' and 过帐日期<$年初日期 then 0-带符号的本位币金额 else 0 end) as 年初收入,
sum(case when 明细帐.总账科目长文本 like '主营业务成本%' and 过帐日期<$年初日期  then 带符号的本位币金额 else 0 end) as 年初成本,
sum(case when 明细帐.总账科目长文本 like '专项储备%'  then 带符号的本位币金额 else 0 end) as 专项储备余额,
sum(case when 明细帐.总账科目长文本 like '%合同结算%' or 明细帐.总账科目长文本 like '合同资产\工程款（已完工未结算）' or 明细帐.总账科目长文本 like '%已结算未完工%' then 带符号的本位币金额 else 0 end) as 合同余额,
sum(case when 明细帐.总账科目长文本 like '合同履约成本%' and 明细帐.总账科目长文本 not like '合同履约成本%结转%' then 带符号的本位币金额 else 0 end) as 合同履约成本余额,
sum(case when 明细帐.总账科目长文本 like '%预计负债\亏损合同%'  then 带符号的本位币金额 else 0 end) as 预计负债亏损合同,
sum(case when 明细帐.总账科目长文本 like '原材料%'  then 带符号的本位币金额 else 0 end) as 原材料,
sum(case when 明细帐.总账科目长文本 like '合同履约成本%'  then 带符号的本位币金额 else 0 end) as 成本余额,
sum(case when 明细帐.总账科目长文本 like '应付账款%暂估%'  then 带符号的本位币金额 else 0 end) as 暂估应付余额,
sum(case when 明细帐.总账科目长文本 like '研发支出%'  then 带符号的本位币金额 else 0 end) as 研发支出,
sum(case when 明细帐.总账科目长文本 like '内部存款\非货币交易'  then 带符号的本位币金额 else 0 end) as 本利润非货币交易未平,
sum(case when 明细帐.总账科目长文本 like '%可用存款%' or 明细帐.总账科目长文本 like '银行存款%'  then 带符号的本位币金额 else 0 end) as 原始存量,
sum(case when 明细帐.总账科目长文本 like '%内部往来\内部借贷%'  then 带符号的本位币金额 else 0 end) as 内部借款,
sum(case when 明细帐.总账科目长文本 like '应付账款\应付供应链融资款'  then 带符号的本位币金额 else 0 end) as 保理借款,
sum(case when (明细帐.总账科目长文本 like '%内部往来\其他' or 明细帐.总账科目长文本 like '%内部往来\代垫费用')  and (客户描述 in ('总部客商名称')) then 带符号的本位币金额 else 0 end) as 内部往来挂总部,
sum(case when (明细帐.总账科目长文本 like '%内部往来\其他' or 明细帐.总账科目长文本 like '%内部往来\代垫费用') then 带符号的本位币金额 else 0 end) - 内部往来挂总部 as 内部往来挂经理部,
sum(case when 明细帐.总账科目长文本 like '机关划转费用科目' then 带符号的本位币金额 else 0 end) as 现场维护费,
内部往来挂经理部+现场维护费 as 内部往来需调整,
sum(case when ((明细帐.总账科目长文本 like '应收账款%进度%' or 明细帐.总账科目长文本 like '合同资产%质保金%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计确权,
0-sum(case when (明细帐.总账科目长文本 like '应收账款%进度%' and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计收款,
0-sum(case when ((明细帐.总账科目长文本 like '应付账款%' and 明细帐.总账科目长文本 not like '%税%' and 明细帐.总账科目长文本 not like '%暂估%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计分供结算,
sum(case when ((明细帐.总账科目长文本 like '应付账款%' and 明细帐.总账科目长文本 not like '%税%' and 明细帐.总账科目长文本 not like '%暂估%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计分供付款,
sum(case when 明细帐.总账科目长文本 like '%分包工程支出%'  then 带符号的本位币金额 else 0 end) as 累计分包工程支出,
sum(case when 明细帐.总账科目长文本 like '%直接人工费%'  then 带符号的本位币金额 else 0 end) as 累计直接人工费,
sum(case when 明细帐.总账科目长文本 like '%直接材料费%'  then 带符号的本位币金额 else 0 end) as 累计直接材料费,
sum(case when 明细帐.总账科目长文本 like '%机械使用费%'  then 带符号的本位币金额 else 0 end) as 累计机械使用费,
sum(case when 明细帐.总账科目长文本 like '%其他直接费用%'  then 带符号的本位币金额 else 0 end) as 累计其他直接费用,
sum(case when (明细帐.总账科目长文本 like '%可用存款%' or 明细帐.总账科目长文本 like '银行存款%') and 过帐日期 < $年初日期 then 带符号的本位币金额 else 0 end) as 期初原始存量,
sum(case when 明细帐.总账科目长文本 like '%内部往来\内部借贷%' and 过帐日期 <$年初日期  then 带符号的本位币金额 else 0 end) as 期初内部借款,
sum(case when 明细帐.总账科目长文本 like '应付账款\应付供应链融资款' and 过帐日期 < $年初日期  then 带符号的本位币金额 else 0 end) as 期初保理借款,
sum(case when 明细帐.总账科目长文本 like '%内部往来\其他' and (客户描述 in ('总部客商名称')) and 过帐日期 <$年初日期 then 带符号的本位币金额 else 0 end) as 期初内部往来挂总部,
期初原始存量+期初内部借款+期初保理借款+期初内部往来挂总部 as 期初快速资金存量,
原始存量+内部借款+保理借款+内部往来挂总部 as 快速资金存量,
累计收款 as 开累资金流入,
累计收款-快速资金存量 as 开累资金流出,
0-sum(case when ((明细帐.总账科目长文本 like '应收账款%进度%' and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) ) and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期收款,
期初快速资金存量+本期收款-快速资金存量 as 本期流出,
0-sum(case when 科目分类2 like '%利润%' then 带符号的本位币金额 else 0 end) as 未含本期毛利累计利润,
0-sum(case when 科目分类2 like '%利润%' and 过帐日期 < $年初日期 then 带符号的本位币金额 else 0 end) as 年初累计利润,
sum(case when (明细帐.总账科目长文本 like '%合同结算%' or 明细帐.总账科目长文本 like '合同资产\工程款（已完工未结算）' or 明细帐.总账科目长文本 like '%已结算未完工%') and 过帐日期<$年初日期 then 带符号的本位币金额 else 0 end) as 年初合同余额,
sum(case when 明细帐.总账科目长文本 like '%分包工程支出%' and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期分包工程支出,
sum(case when 明细帐.总账科目长文本 like '%直接人工费%' and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期直接人工费,
sum(case when 明细帐.总账科目长文本 like '%直接材料费%' and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期直接材料费,
sum(case when 明细帐.总账科目长文本 like '%机械使用费%'  and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期机械使用费,
sum(case when 明细帐.总账科目长文本 like '%其他直接费用%'  and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期其他直接费用
FROM 明细帐 
LEFT JOIN 科目对照 on 明细帐.总账科目长文本=科目对照.总账科目长文本  
left join 主数据 on 明细帐.利润中心 = 主数据.利润中心
where  过帐日期<=$期末日期 and WBS元素 != '' and WBS元素 not like 'QCQH%'
GROUP by  明细帐.利润中心,明细帐.WBS元素

'''

按利润中心计算科目检查='''with 
a as (
select 
any_value(利润中心组名称) as 利润中心组名称,
any_value(利润中心名称) as 利润中心名称,
any_value(利润中心) as 利润中心,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%可用存款%' or 科目余额表第二期.总账科目长文本 like '银行存款%'  then 期末余额 else 0 end),4) as 原始存量,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\内部借贷%'  then 期末余额 else 0 end),4) as 内部借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款\应付供应链融资款%'  then 期末余额 else 0 end),4) as 保理借款,
原始存量+内部借款+保理借款 as 净存量未计算保理借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '专项储备%'  then 期末余额 else 0 end),4) as 专项储备余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%合同结算%' or 科目余额表第二期.总账科目长文本 like '合同资产\工程款（已完工未结算）' or 科目余额表第二期.总账科目长文本 like '%已结算未完工%' then 期末余额 else 0 end),4) as 合同余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '合同履约成本%'  then 期末余额 else 0 end),4) as 成本余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '研发支出%'  then 期末余额 else 0 end),4) as 研发支出,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%本利润中心%'  then 期末余额 else 0 end),4) as 本利润中心余额,
round(sum(case when (科目余额表第二期.总账科目长文本 like '应收账款%' or 科目余额表第二期.总账科目长文本 like '合同资产%质保金') and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年确权额,
round(sum(case when (科目余额表第二期.总账科目长文本 like '应收账款%' or 科目余额表第二期.总账科目长文本 like '合同资产%质保金') and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年收款额,
from 科目余额表第二期
GROUP by  利润中心),
b as (
select
any_value(利润中心) as 利润中心,
round(sum(case when (总账科目长文本 like '应收账款%' or 总账科目长文本 like '合同资产%质保金') and 总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年确权额,
round(sum(case when (总账科目长文本 like '应收账款%' or 总账科目长文本 like '合同资产%质保金') and 总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年收款额,
from 科目余额表第一期
GROUP by 利润中心
),
c as (
select 
利润中心组名称,利润中心名称,a.利润中心,原始存量,内部借款,保理借款,净存量未计算保理借款,专项储备余额,合同余额,成本余额,研发支出,
a.本年确权额+b.本年确权额 as 累计确权,
a.本年收款额+b.本年收款额 as 累计收款,
from a left JOIN b on a.利润中心 = b.利润中心
),
d as (select sum(结算额) as 总包结算额,sum(付款额) as 总包付款额,sum(暂估额) as 总包暂估额,any_value(利润中心) AS 利润中心 from 内部对账 GROUP by 利润中心)
select *,c.累计确权-d.总包结算额 as 结算差额,c.累计收款-d.总包付款额 as 付款差额 from c left join d on c.利润中心=d.利润中心'''

按月统计时段值='''-- Active: 1749688970779@@127.0.0.1@3306
-- Active: 1748239704126@@127.0.0.1@3306

SELECT 
    DATE_TRUNC('month', 过帐日期) AS month,        -- 提取月份
    round(-SUM(case when 明细帐.总账科目长文本 like '主营业务收入%' then 明细帐.带符号的本位币金额 else 0 end)/10000,2) AS 当月收入,                     -- 当月收入
    round(-SUM(SUM(case when 明细帐.总账科目长文本 like '主营业务收入%' then 明细帐.带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 累计收入,
    round(-SUM(case when 科目分类2 like '%本年利润%' then 明细帐.带符号的本位币金额 else 0 end)/10000,2) AS 当月利润,   
    round(-SUM(SUM(case when 科目分类2 like '%本年利润%' then 明细帐.带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 累计利润,
    CASE EXTRACT(MONTH FROM DATE_TRUNC('month', 过帐日期))
    WHEN 1 THEN '一月'
    WHEN 2 THEN '二月'
    WHEN 3 THEN '三月'
    WHEN 4 THEN '四月'
    WHEN 5 THEN '五月'
    WHEN 6 THEN '六月'
    WHEN 7 THEN '七月'
    WHEN 8 THEN '八月'
    WHEN 9 THEN '九月'
    WHEN 10 THEN '十月'
    WHEN 11 THEN '十一月'
    WHEN 12 THEN '十二月'
  END AS 中文月份
FROM 明细帐 
left join 科目对照 on 明细帐.总账科目长文本=科目对照.总账科目长文本
where 过帐日期 >= '2025-01-01'
GROUP BY DATE_TRUNC('month', 过帐日期)
ORDER BY month
'''

按月统计累计值='''select * from (
SELECT 
    DATE_TRUNC('month', 过帐日期) AS month,        -- 提取月份
    round(SUM(SUM(case when 总账科目长文本 like '合同资产%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 累计合同资产,
    round(SUM(SUM(case when 总账科目长文本 like '%存款%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 存量,
    round(SUM(SUM(case when 总账科目长文本 like '应收%票据%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 应收票据,
    round(SUM(SUM(case when 总账科目长文本 like '应付%票据%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 应付票据,
    round(SUM(SUM(case when 总账科目长文本 like '%应付%保理%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 应付保理,
    round(SUM(SUM(case when 总账科目长文本 like '短期借款%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 借款,
    round(SUM(SUM(case when 总账科目长文本 like '其他应收款%待确认进项税%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 其他应收款待确认进项税,
    -round(SUM(SUM(case when 总账科目长文本 like '应收账款%待转销%' or 总账科目长文本 like '应交税费%待转销%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 应交税费待转销,
    存量+应收票据-应付保理-应付票据-借款 AS 净流量,
    CASE EXTRACT(MONTH FROM DATE_TRUNC('month', 过帐日期))
    WHEN 1 THEN '一月'
    WHEN 2 THEN '二月'
    WHEN 3 THEN '三月'
    WHEN 4 THEN '四月'
    WHEN 5 THEN '五月'
    WHEN 6 THEN '六月'
    WHEN 7 THEN '七月'
    WHEN 8 THEN '八月'
    WHEN 9 THEN '九月'
    WHEN 10 THEN '十月'
    WHEN 11 THEN '十一月'
    WHEN 12 THEN '十二月'
  END AS 中文月份
FROM 明细帐 
GROUP BY DATE_TRUNC('month', 过帐日期)) as t where t.month >= '2025-01-01'
ORDER BY month'''

收支台账='''select 
any_value(利润中心描述) as 利润中心名称,
any_value(利润中心) as 利润中心,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '应交税费\增值税\预交增值税\建筑服务%' )then 带符号的本位币金额 else null end) as 预缴税金,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '税金及附加\%' )then 带符号的本位币金额 else null end) as 本期税金,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '合同履约成本%职工薪酬%' )then 带符号的本位币金额 else null end) as 薪酬,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '合同履约成本%办公%' )then 带符号的本位币金额 else null end) as 办公,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '合同履约成本%差旅%' )then 带符号的本位币金额 else null end) as 差旅费,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '合同履约成本%业务招待%' )then 带符号的本位币金额 else null end) as 业务招待费
FROM 明细帐
where (过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')
GROUP by  利润中心'''

收款台账='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       SUM(case when (总账科目长文本 LIKE '%非货币交易%' ) then 带符号的本位币金额 ELSE NULL END) AS 本利润中心,
       SUM(CASE WHEN (总账科目长文本 LIKE '%可用存款%' or 总账科目长文本 LIKE '%银行存款%') THEN 带符号的本位币金额 ELSE NULL END) AS 内行或存款,
       SUM(CASE WHEN (总账科目长文本 LIKE '%内部往来%其他') THEN 带符号的本位币金额 ELSE NULL END) AS 内部往来其他收款,
       any_value(case when 总账科目长文本  like '%可用存款%' or 总账科目长文本  like '%内部往来%其他' then 客户描述 else null end) as 内行客商
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(输入日期) as 输入日期,
any_value(客户) as 客户,any_value(客户描述) as 客户描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
0-sum(带符号的本位币金额) as '总收款金额'
FROM 明细帐
WHERE (总账科目长文本 LIKE '应收账款%款%'  or 总账科目长文本 LIKE '合同资产%质保金%' ) and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,客户,合同
),
c as (
select  b.财年,过帐日期,输入日期,b.凭证编号,b.利润中心,利润中心描述,客户,客户描述,合同,合同文本描述,文本,中台单据号,总收款金额,本利润中心,内行或存款,内部往来其他收款,内行客商
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年)
INSERT OR REPLACE INTO 收款台账 SELECT * FROM c
'''

汇总上年度应收='''
select 
SUM(case when (总账科目长文本 LIKE '应收账款\工程款%' or 总账科目长文本 LIKE '合同资产\质保金%') then 本期借方金额 ELSE NULL END) AS 累计应收,
SUM(case when (总账科目长文本 LIKE '应收账款\工程款%') then 本期贷方金额 ELSE NULL END) AS 累计已收,
SUM(case when (总账科目长文本 LIKE '%可用存款' or 总账科目长文本 LIKE '%银行存款%' or  总账科目长文本 LIKE '%内部借贷%' or
(总账科目长文本 LIKE '%内部往来%其他%' and (客户名称 = '中建三局安装工程有限公司' or 客户名称 = '中建三局集团有限公司安装事业部')))
then 期末余额 ELSE NULL END) as 资金余额,
any_value(利润中心)
from df
GROUP by 利润中心

'''

确权台账='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       0-SUM(case when (总账科目长文本 LIKE '%销项%' ) then 带符号的本位币金额 ELSE NULL END) AS 销项税
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
--
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(合同) as 合同,
any_value(输入日期) as 输入日期,
any_value(客户) as 客户,any_value(客户描述) as 客户描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
sum(带符号的本位币金额) as 含税确权额
FROM 明细帐
WHERE (总账科目长文本 LIKE '应收账款%款%'  or 总账科目长文本 LIKE '合同资产%质保金%') and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,客户,合同
),
c as (
select  b.财年,过帐日期,输入日期,b.凭证编号,b.利润中心,利润中心描述,客户,客户描述,合同,文本,中台单据号,含税确权额,销项税
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年)
INSERT OR REPLACE INTO 确权台账 SELECT * FROM c'''

科目余额表='''select 
any_value(总账科目) as 总账科目,
any_value(总账科目长文本) as 总账科目长文本,
any_value(利润中心) as 利润中心,
any_value(利润中心描述) as 利润中心名称,
any_value(WBS元素) as WBS元素,
any_value(WBS元素描述) as WBS元素描述,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商名称,
any_value(客户) as 客户,
any_value(客户描述) as 客户名称,
sum(case when (过帐日期<'期初日期留 00:00:00')  then 带符号的本位币金额 else null end) as 期初余额,
sum(带符号的本位币金额) as 期末余额,
sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留 00:00:00') )) then 带符号的本位币金额 else null end) as 本期贷方发生额,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留 00:00:00') )) then 带符号的本位币金额 else null end) as 本期借方发生额,
sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X')) ) then 带符号的本位币金额 else null end) as 累计贷方发生额,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X')) ) then 带符号的本位币金额 else null end) as 累计借方发生额,
(case when 期末余额>0.0001 then '借' when 期末余额<-0.0001 then '贷' else '平' end) as 期末方向 
from 明细帐 where  文本 != '自动清账剩余项目'  and 过帐日期<='期末日期留 00:00:00' GROUP BY 总账科目长文本,利润中心,合同,供应商,客户,WBS元素'''

科目对照='''with a as (
    select 
    any_value(总账科目长文本) as 总账科目长文本 ,
    '' as 科目分类1,
    '' as 科目分类2,
    '' as 科目方向
     from 明细帐
     GROUP BY 总账科目长文本
)
--只能插入，不能更新
INSERT or ignore INTO 科目对照 SELECT *  FROM a '''

账期查询='''with a as (
select 
any_value(利润中心描述) as 利润中心描述,
any_value(总账科目长文本) as 总账科目长文本,
any_value(核对线索) as 核对线索,
any_value(业务范围) as 业务范围,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(CAST(付款起算日期 as DATE)) as 起算日期,
any_value(cast(付款天数 as integer)) as 天1,
any_value(利润中心) as 利润中心,
any_value(WBS元素) as WBS元素,
any_value(付款条款) as 付款条件,
any_value(公司代码) as 公司,
any_value(总账科目) as 总账科目,
sum(case when 过帐日期 <= '替换日期' then 0-带符号的本位币金额 else null end) as 金额,
sum(0-带符号的本位币金额 ) as 当前参考额,
(起算日期 + 天1) AS 到期日
from 明细帐  
where  总账科目长文本 like '应付账款%' and  总账科目长文本 not like '%税%' and (清帐凭证='' or (清帐凭证!='' and TRY_CAST(清帐日期 as date)>'替换日期')) and 付款起算日期 SIMILAR TO '^\d{4}-\d{2}-\d{2}$' 
GROUP BY 总账科目长文本,利润中心,核对线索,供应商,合同,付款起算日期,付款天数)
--
select * from a where (金额<-1 or 金额>1) and 到期日<='替换日期' '''

资金付款分类='''select 
(case when 供应商类型 like '%分包%' then '分包' when 供应商类型 like '%劳务%' then '劳务' when 供应商类型 like '%购货%' then '材料' when 供应商类型 like '%设备%' then '设备' else '其他' end) as 类型,
sum(总付款金额) as 总付款,
sum(case when a.内行客商 is not NULL and b.利润中心描述 is NULL  then a.内行或存款 else 0 end) as 货币付款,
sum(case when 文本 like '%代付%'  then a.本利润中心 else 0 end) as 本项目代付付款,
sum(case when a.内行客商 is not NULL and b.利润中心描述 is not NULL and 文本 like '%代付%'   then a.内行或存款 else 0 end) as 跨项目代付付款,
sum(case when a.内行客商 is not NULL and b.利润中心描述 is not NULL and 文本 like '%商票%'   then a.内行或存款 else 0 end) as 商票付款,
sum(case when a.内行客商 is not NULL and b.利润中心描述 is not NULL and 文本 like '%银票%'   then a.内行或存款 else 0 end) as 银票付款,
sum(扣履约保证金) as 扣保证金,
sum(供应链保理) as 保理,
sum(冲成本) as 冲减成本,
总付款 - 货币付款 - 本项目代付付款 - 跨项目代付付款 - 商票付款 - 银票付款 - 扣保证金 - 保理 - 冲减成本 as 其他
from 付款台账 as a 
left join 主数据 as b on a.内行客商 = b.利润中心描述
where a.过帐日期 between $1 and $2
group by 类型'''

资金内行其他='''select 内行查询.*
from 内行查询 left join 付款台账 on 内行查询.凭证编号 = 付款台账.凭证编号 and 内行查询.利润中心 = 付款台账.利润中心 and 内行查询.财年 = 付款台账.财年
left join 收款台账 on 内行查询.凭证编号 = 收款台账.凭证编号 and 内行查询.利润中心 = 收款台账.利润中心 and 内行查询.财年 = 收款台账.财年
where 付款台账.凭证编号 is null and 收款台账.凭证编号 is null and 内行查询.过帐日期 between $1 and $2'''

资金存量变化='''select
sum(case when (总账科目长文本 like '%可用存款%' or 总账科目长文本 like '银行存款%') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 原始存量,
sum(case when (总账科目长文本 like '%内部往来\内部借贷%' or 总账科目长文本 like '短期借款%') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 短期借款,
sum(case when (总账科目长文本 like '应付账款\应付供应链融资款') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 应付保理,
sum(case when (总账科目长文本 like '%内部往来\其他' or 总账科目长文本 like '%内部往来\代垫费用')  and (客户描述 in ('总部客商名称')) and 过帐日期 < $1 then 带符号的本位币金额 else 0 end) as 内部往来挂总部,
sum(case when (总账科目长文本 like '应付票据%') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 应付票据,
sum(case when (总账科目长文本 like '应收票据%') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 应收票据,
原始存量+应收票据+应付票据+短期借款+应付保理+内部往来挂总部 as 净存量,
sum(case when (总账科目长文本 like '%可用存款%' or 总账科目长文本 like '银行存款%') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 原始存量2,
sum(case when (总账科目长文本 like '%内部往来\内部借贷%' or 总账科目长文本 like '短期借款%') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 短期借款2,
sum(case when (总账科目长文本 like '应付账款\应付供应链融资款') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 应付保理2,
sum(case when (总账科目长文本 like '%内部往来\其他' or 总账科目长文本 like '%内部往来\代垫费用')  and (客户描述 in ('总部客商名称')) and 过帐日期 < $2 then 带符号的本位币金额 else 0 end) as 内部往来挂总部2,
sum(case when (总账科目长文本 like '应收票据%') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 应收票据2,
sum(case when (总账科目长文本 like '应付票据%') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 应付票据2,
原始存量2+应收票据2+应付票据2+短期借款2+应付保理2+内部往来挂总部2 as 净存量2
from 明细帐 '''

资金收款='''select 
sum(case when a.内行客商 is not NULL and b.利润中心描述 is NULL  then a.内行或存款 else 0 end) as 货币收款,
sum(case when 文本 like '%抵房%' then 总收款金额 else 0 end) as 抵房收款,
sum(case when 文本 like '%商票%' or 文本 like '%银票%' then 总收款金额 else 0 end) as 票据收款,
sum(case when 文本 like '%代付%' then 总收款金额 else 0 end) as 代付收款,
sum(总收款金额) as 合计,
合计-货币收款-抵房收款-票据收款-代付收款 as 其他收款
from 收款台账 as a 
left join 主数据 as b on a.内行客商=b.利润中心描述
where a.过帐日期 between $1 and $2'''

资金整理分类='''select round(sum(内行金额),2) as 内行金额, any_value(单据分类) as 单据分类 from 资金整理  where 过帐日期 between ? and ? group by 单据分类'''

