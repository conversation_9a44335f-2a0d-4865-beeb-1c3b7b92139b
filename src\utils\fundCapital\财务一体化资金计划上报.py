
from playwright.sync_api import Page,Locator
from playwright.sync_api import ChromiumBrowserContext
import time
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.Browser.Browser as browser
from src.utils.Excel.excel import table
import duckdb
import src.base.settings as settings
import re
import pandas as pd
import src.utils.fileui as fileui
from src.utils.DB.midIntrSQLiteDB import excelDB


class planUpload:
    def __init__(self):
        self.stfillcollection='''
(js_need,Parameter)=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
let colCount = sheet.getColumnCount(); // 获取最大列数
let headRow=[];
for (var i = 0; i<colCount; i++) { // 遍历行
    headRow.push(sheet.getValue(1, i)); //获取表头
};
let currencyIdx=headRow.indexOf("合同币种");
let collectionIdx=headRow.indexOf("新增计划收款金额")+1;
let remarkIdx=headRow.indexOf("备注");

for (var i = 0; i<rowCount; i++) { // 遍历行
if (sheet.getValue(i, currencyIdx)=="CNY") {sheet.setValue(i,collectionIdx,0)};
};
sheet.setValue(4,collectionIdx,Parameter[0]);
sheet.setValue(4,remarkIdx,Parameter[1]);
}
'''     
        self.stchoose='''
js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
for (var i = 0; i<rowCount; i++) { 
if (sheet.getValue(i, 2)=="借入方其他资金筹集") {var nuM=i+1}
}
sheet.setActiveCell(nuM, 4);//使得单元格出现在屏幕
sheet.showCell(nuM,3);
cellRect=sheet.getCellRect(nuM,4);//是表格相对元素
x=cellRect.x+cellRect.width*0.95;
y=cellRect.y+cellRect.height*0.5;
var arr=new Array(2);
arr[0]=x;
arr[1]=y;
return arr;
}
'''
        self.stfillbrrow='''
(js_need,borrowMoney)=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
for (var i = 0; i<rowCount; i++) { 
if (sheet.getValue(i, 2)=="借入方其他资金筹集") {var nuM=i+1}
}
sheet.setValue(nuM,7,borrowMoney);
}
'''
        self.stfocus='''
js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
for (var i = 0; i<rowCount; i++) { 
if (sheet.getValue(i, 2)=="借入方其他资金筹集") {var nuM=i}
}

sheet.setActiveCell(nuM, 5)
}
'''
        self.st_deleteALLrownotcontract='''
js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
var colCount = sheet.getColumnCount(); // 获取最大列数
for (var i = rowCount-3; i >= 7; i--) { // 遍历行
if(sheet.getValue(i, 6)!=null && sheet.getValue(i, 5)=="CNY"){sheet.deleteRows(i, 2);break;};
};
}
'''
        self.st2='''
js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
return sheet.getValue(4, 5);}
''' #获取资金计划余额

        self.st='''
(js_need,Parameter)=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
var colCount = sheet.getColumnCount(); // 获取最大列数
var totalPayment = 0;
headRow=[];
for (var i = 0; i<colCount; i++) { // 遍历列
    headRow.push(sheet.getValue(1, i)); //获取表头
}
let contractCodeIdx=headRow.indexOf("合同编码");
let currencyIdx=headRow.indexOf("合同币种");
let paymentIdx=headRow.indexOf("新增计划付款金额")+1;
let remarkIdx=headRow.indexOf("备注");
let settlementAmountIdx=headRow.indexOf("结算金额");
let predictIdx=headRow.indexOf("结算金额")+1;
let contractPaymentRatioIdx=headRow.indexOf("合同付款比例(%)");
let cumulativePaymentAmountIdx=headRow.indexOf("累计已付金额");
let lastMonthOccupiedUnusedAmountIdx=headRow.indexOf("上月已占用未使用金额");
let lastMonthUnusedAmountIdx=headRow.indexOf("上月未占用未使用金额");
let cumulativeApprovalAmountIdx=headRow.indexOf("累计批复数(含本次)");
let monthlyPaymentAmountIdx=headRow.indexOf("当月计划付款金额合计");
for (var i = 0; i<rowCount; i++) { // 遍历行
if (sheet.getValue(i, contractCodeIdx) in Parameter[0]) {sheet.setValue(i,paymentIdx,Parameter[0][sheet.getValue(i, 2)])};
if (sheet.getValue(i, contractCodeIdx) in Parameter[1]) {sheet.setValue(i,remarkIdx,Parameter[1][sheet.getValue(i, 2)])};
if (sheet.getValue(i, currencyIdx)=="CNY") {var predict=-(sheet.getValue(i, settlementAmountIdx)*sheet.getValue(i, contractPaymentRatioIdx)-sheet.getValue(i, cumulativePaymentAmountIdx)-sheet.getValue(i, lastMonthOccupiedUnusedAmountIdx)-sheet.getValue(i, paymentIdx)-sheet.getValue(i, lastMonthUnusedAmountIdx)-sheet.getValue(i, cumulativeApprovalAmountIdx))/sheet.getValue(i, contractPaymentRatioIdx).toFixed(2)+1;totalPayment=totalPayment+sheet.getValue(i, cumulativeApprovalAmountIdx)+sheet.getValue(i, monthlyPaymentAmountIdx)};
if (sheet.getValue(i, currencyIdx)=="CNY" && sheet.getValue(i, settlementAmountIdx)*sheet.getValue(i, contractPaymentRatioIdx)-sheet.getValue(i, cumulativePaymentAmountIdx)-sheet.getValue(i, lastMonthOccupiedUnusedAmountIdx)-sheet.getValue(i, paymentIdx)-sheet.getValue(i, lastMonthUnusedAmountIdx)-sheet.getValue(i, cumulativeApprovalAmountIdx)<0) {sheet.setValue(i,predictIdx,predict)};
};
return totalPayment;
}
'''

    
    def __fillOneProject(self,page:Page,planDict: dict,project_name,project_unit,collection,batch,dRemark:dict,mainRemark):
        page.locator("//span[contains(text(),'-"+project_unit+"') and contains(text(),'000')]/parent::div").click() #同时包含000*-
        
        tr=cscec.getTr(page,"项目名称")
        tr.locator("//div[contains(text(),'"+project_name+"')]").dblclick()
        tr.locator("//div[contains(text(),'"+project_name+"')]").click()
        try:
            page.locator("//div[contains(text(),'"+project_name+"')]/parent::td/following-sibling::td[2]//div[contains(text(),'"+batch+"')]").dblclick()
        except Exception as e:
            page.locator("//span[contains(text(),'增加')]").click()
            page.locator("//div[contains(text(),'"+project_name+"')]/parent::td/following-sibling::td[2]//div[contains(text(),'"+batch+"')]").dblclick()
        
        page.locator("//span[contains(text(),'汇总表2')]/parent::li").click()
        js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
        lastFundBalance=js_need.evaluate(self.st2)

        page.locator("//span[contains(text(),'项目流入流出表')]/parent::li").click()
        page.locator("//span[contains(text(),'项目流入表')]/parent::li").click()
        js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
        page.locator("//span[contains(text(),'编辑')]/parent::div/parent::div/parent::div/parent::div").click()

        Parameter=[]
        Parameter.append(collection)
        Parameter.append(mainRemark)
        js_need.evaluate(self.stfillcollection,Parameter)

        page.locator("//span[contains(text(),'项目流出表')]/parent::li").click()
        js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
        
        Parameter=[]
        Parameter.append(planDict)
        Parameter.append(dRemark)
        totalPayment=js_need.evaluate(self.st,Parameter)

        cscec.getVisible(page,"//span[contains(text(),'保存')]/parent::div/parent::div/parent::div/parent::div").click()
        msg=cscec.locatorDigalog(page,"提示").text_content()
        if "可用支出限额" in msg:
            pattern = r'\b[-+]?\d*\.\d+|\b[-+]?\d+\b'
            matches = re.findall(pattern, msg)
            borrowMoney=str(float(matches[1])-float(matches[2])+1000)
            cscec.clickDigalog(page,"提示")
            page.locator("//span[contains(text(),'其他附表')]/parent::li").click()
            page.locator("//span[contains(text(),'其他流入流出表')]/parent::li").click()
            js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
            page.locator("//span[contains(text(),'编辑')]/parent::div/parent::div/parent::div/parent::div").click()
            time.sleep(1)
            js_need.evaluate(self.stfocus)
            js_need.click(button='right')
            page.get_by_text("插入浮动行...").click()
            js_need.evaluate(self.stfillbrrow,borrowMoney)
            point=js_need.evaluate(self.stchoose)
            page.mouse.click(point[0]+js_need.bounding_box()['x'],point[1]+js_need.bounding_box()['y'])
            page.keyboard.press('ArrowDown')
            page.keyboard.press('Enter')  #选择内部结算户
            cscec.getVisible(page,"//span[contains(text(),'保存')]/parent::div/parent::div/parent::div/parent::div").click()
            cscec.clickDigalog(page,"提示")
        else:
            cscec.clickDigalog(page,"提示")
        cscec.closeTab(page)

    def getTemplate(self,toFile=False):
        conn = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=True)
        df1=conn.execute("select *,0 as 本次付款金额,'' as 备注 from 一体化合同台账").df()
        df2=conn.execute("select any_value(组织机构名称) as 组织机构名称, any_value(项目名称) as 项目名称,any_value(项目编号) as 项目编号,0 as 本次收款,'' as 总备注,'' as 是否上报 from 一体化合同台账 group by 组织机构名称,项目名称,项目编号 ").df()
        if toFile:
            df1.fillna("",inplace=True)
            df2.fillna("",inplace=True)
            return {"计划上传总表":[df2.columns.tolist()]+df2.values.tolist(),"计划上传明细":[df1.columns.tolist()]+df1.values.tolist()}
        path=fileui.select_directory(title="选择资金计划上传模板保存路径")
        with pd.ExcelWriter(path+'/一体化计划上传模板.xlsx') as writer:
            df2.to_excel(writer, sheet_name='计划上传总表', index=False)
            df1.to_excel(writer, sheet_name='计划上传明细', index=False)
        print("模板保存在"+path+'/一体化计划上传模板.xlsx')

    def upload(self,batch):
        conn=excelDB().conn
        df0=pd.read_sql("SELECT * FROM 计划上传总表",conn)
        df1=pd.read_sql("SELECT * FROM 计划上传明细",conn)
        B=browser.myBrowser("cscec")
        page=B.page
        cscec.toFunction(page,"司库系统","资金预算","明细财务计划编制")
        page.locator("//span[contains(text(),'-')]/parent::div/parent::div/parent::td/div[1]").first.click()#起等待作用
        tdNext=page.locator("//span[contains(text(),'-')]/parent::div/parent::div/parent::td/div")
        #点击展开，使用递归将树形全部展开
        def clickOrg(tdNext:Locator):
            loopCount=tdNext.count()
            for orgI in range(loopCount):
                if 'QxYMABAKUSJzQ00EBnAAAAAElFTkSuQmCC' in tdNext.nth(orgI).locator("xpath=/div[1]/img[3]").get_attribute("style"):
                    tdNext.nth(orgI).locator("xpath=/div[1]/img[2]").click()
                    time.sleep(0.5)
                    tdNexts2=tdNext.nth(orgI).locator("xpath=/div[2]/div")
                    clickOrg(tdNexts2)
        clickOrg(tdNext)
        for index, row in df0.iterrows():
            print("持续运行")
            if row["是否上报"]=="是":
                df2=df1[df1['项目名称']==row['项目名称']]
                pay_dict = dict(zip(df2['合同编号'], df2['本次付款金额']))
                remarks_dict = dict(zip(df2['合同编号'], df2['备注']))
                project_name=row["项目名称"]
                project_unit=row["组织机构名称"]
                collection=row["本次收款"]
                batch=batch
                project_code=row["项目编号"]
                mainRemark=row["总备注"]
                tryCount=3
                while tryCount>0:
                    try:
                        self.__fillOneProject(page,pay_dict,project_name,project_unit,collection,batch,remarks_dict,mainRemark)
                        tryCount=-1
                    except Exception as e:
                        print("报错一次")
                        print(row["项目名称"])
                        cscec.closeTab(page)
                        tryCount=tryCount-1
                if tryCount==0:
                    cscec.closeTab(page)
    
    def queryData(self):
        conn=excelDB()
        df1=pd.read_sql("SELECT * FROM 计划上传总表",conn.conn)
        df2=pd.read_sql("SELECT * FROM 计划上传明细",conn.conn)
        df1.fillna("",inplace=True)
        df2.fillna("",inplace=True)
        conn.conn.close()
        return {"计划上传总表":[df1.columns.tolist()]+df1.values.tolist(),"计划上传明细":[df2.columns.tolist()]+df2.values.tolist()}
    def wirteData(self,data):
        conn=excelDB()
        title1=data["计划上传总表"][0]
        processTitle1=[f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(title1)]
        df1=pd.DataFrame(data["计划上传总表"][1:],columns=processTitle1)
        df1.to_sql("计划上传总表", conn.conn, if_exists='replace', index=False)
        title2=data["计划上传明细"][0]
        processTitle2=[f"unnamed_{i}" if col is None or col=="" else col for i, col in enumerate(title2)]
        df2=pd.DataFrame(data["计划上传明细"][1:],columns=processTitle2)
        df2.to_sql("计划上传明细", conn.conn, if_exists='replace', index=False)
        conn.conn.close()



